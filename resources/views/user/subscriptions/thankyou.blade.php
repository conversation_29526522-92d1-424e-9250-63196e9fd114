@extends('layouts.app')

@section('title', 'Subscription Confirmed')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-body text-center">
                    <div class="mb-4">
                        <i class="fas fa-check-circle text-success" style="font-size: 5rem;"></i>
                    </div>
                    
                    <h2>Thank You!</h2>
                    <p class="lead">Your subscription has been confirmed.</p>
                    
                    @if(session('success'))
                        <div class="alert alert-success">
                            {{ session('success') }}
                        </div>
                    @endif
                    
                    <div class="card mt-4">
                        <div class="card-header bg-light">
                            <h5 class="mb-0">Subscription Details</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6 text-left">
                                    <p><strong>Plan:</strong> {{ $subscription->subscriptionPlan->name }}</p>
                                    <p><strong>Tier:</strong> 
                                        <span class="badge badge-{{ $subscription->subscriptionPlan->tier === 'basic' ? 'secondary' : ($subscription->subscriptionPlan->tier === 'premium' ? 'primary' : 'success') }}">
                                            {{ ucfirst($subscription->subscriptionPlan->tier) }}
                                        </span>
                                    </p>
                                    <p><strong>Price:</strong> RM {{ number_format($subscription->subscriptionPlan->price, 2) }} / {{ $subscription->subscriptionPlan->billing_frequency }}</p>
                                </div>
                                <div class="col-md-6 text-left">
                                    <p><strong>Start Date:</strong> {{ $subscription->starts_at->format('d M Y') }}</p>
                                    
                                    @if($subscription->trial_ends_at)
                                        <p><strong>Trial Ends:</strong> {{ $subscription->trial_ends_at->format('d M Y') }}</p>
                                    @endif
                                    
                                    @if($subscription->ends_at)
                                        <p><strong>Expiry Date:</strong> {{ $subscription->ends_at->format('d M Y') }}</p>
                                    @else
                                        <p><strong>Expiry Date:</strong> Never (Ongoing)</p>
                                    @endif
                                </div>
                            </div>
                            
                            @if($subscription->bill_id)
                                <div class="alert alert-info mt-3">
                                    <p class="mb-0"><strong>Payment Reference:</strong> {{ $subscription->bill_id }}</p>
                                </div>
                            @endif

                            @if($subscription->emandateEnrollment)
                                <div class="alert alert-success mt-3">
                                    <h6><i class="fas fa-university mr-2"></i>Direct Debit Setup</h6>
                                    <p class="mb-2"><strong>Status:</strong>
                                        @if($subscription->emandateEnrollment->status === 'new' || $subscription->emandateEnrollment->status === 'pending')
                                            <span class="badge badge-warning">Processing</span>
                                        @elseif($subscription->emandateEnrollment->status === 'active')
                                            <span class="badge badge-success">Active</span>
                                        @else
                                            <span class="badge badge-secondary">{{ ucfirst($subscription->emandateEnrollment->status) }}</span>
                                        @endif
                                    </p>
                                    <p class="mb-2"><strong>Amount:</strong> RM {{ number_format($subscription->emandateEnrollment->amount, 2) }}</p>
                                    <p class="mb-2"><strong>Frequency:</strong>
                                        @switch($subscription->emandateEnrollment->frequency_mode)
                                            @case('DL') Daily @break
                                            @case('WK') Weekly @break
                                            @case('MT') Monthly @break
                                            @case('YR') Yearly @break
                                            @default {{ $subscription->emandateEnrollment->frequency_mode }} @break
                                        @endswitch
                                    </p>
                                    @if($subscription->emandateEnrollment->effective_date)
                                        <p class="mb-0"><strong>Effective Date:</strong> {{ \Carbon\Carbon::parse($subscription->emandateEnrollment->effective_date)->format('d M Y') }}</p>
                                    @endif
                                </div>
                            @endif
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <a href="{{ route('user.subscriptions.index') }}" class="btn btn-primary">
                            <i class="fas fa-list mr-1"></i> View My Subscriptions
                        </a>
                        <a href="{{ route('dashboard') }}" class="btn btn-default ml-2">
                            <i class="fas fa-home mr-1"></i> Go to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection