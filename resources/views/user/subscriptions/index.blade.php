@extends('backend.layout.default')

@section('title', 'Subscription Management')

@push('styles')
<style>
    .billing-toggle-container {
        background-color: #f8f9fa;
        border-radius: 50px;
        padding: 10px;
        display: inline-block;
    }

    .btn-group .billing-toggle {
        border-radius: 50px;
        padding: 8px 20px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .btn-group .billing-toggle.active {
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    }

    .save-badge {
        position: absolute;
        top: -10px;
        right: -10px;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        z-index: 1;
    }
</style>
@endpush

@section('content')
<div class="page-content">
    <section class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Subscription Management</h4>
                </div>
                <div class="card-body">
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            {{ session('success') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif
                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            {{ session('error') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    @endif

                    <ul class="nav nav-tabs" id="subscriptionTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="current-tab" data-bs-toggle="tab" data-bs-target="#current" type="button" role="tab" aria-controls="current" aria-selected="true">Current Subscription</button>
                        </li>
                        @if (auth()->user()->username === 'wowskin')
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="plans-tab" data-bs-toggle="tab" data-bs-target="#plans" type="button" role="tab" aria-controls="plans" aria-selected="false">Available Plans</button>
                        </li>
                        @endif
                        
                    </ul>

                    <div class="tab-content mt-3" id="subscriptionTabsContent">
                        <!-- Current Subscription Tab -->
                        <div class="tab-pane fade show active" id="current" role="tabpanel" aria-labelledby="current-tab">
                            @if($activeSubscription)
                                <div class="row">
                                    <div class="col-md-10 offset-md-1">
                                        <div class="card shadow-sm">
                                            <div class="card-header bg-light">
                                                <h5 class="mb-0">Current Subscription</h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <h4>{{ $activeSubscription->subscriptionPlan->name }}</h4>
                                                        <p class="text-muted">{{ $activeSubscription->subscriptionPlan->description }}</p>

                                                        <div class="mb-3">
                                                            <span class="badge bg-{{ $activeSubscription->subscriptionPlan->tier === 'basic' ? 'secondary' : ($activeSubscription->subscriptionPlan->tier === 'premium' ? 'primary' : 'success') }}">
                                                                {{ ucfirst($activeSubscription->subscriptionPlan->tier) }}
                                                            </span>
                                                            <span class="badge bg-info">
                                                                {{ ucfirst($activeSubscription->subscriptionPlan->billing_frequency) }}
                                                            </span>
                                                            @if(method_exists($activeSubscription, 'onTrial') && $activeSubscription->onTrial())
                                                                <span class="badge bg-warning">Trial</span>
                                                            @endif
                                                            @if(method_exists($activeSubscription, 'canceled') && $activeSubscription->canceled())
                                                                <span class="badge bg-danger">Canceled</span>
                                                            @endif
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="subscription-details">
                                                            <p><strong>Started:</strong> {{ $activeSubscription->starts_at->format('d M Y') }}</p>

                                                            @if(method_exists($activeSubscription, 'onTrial') && $activeSubscription->onTrial())
                                                                <p><strong>Trial Ends:</strong> {{ $activeSubscription->trial_ends_at->format('d M Y') }}</p>
                                                            @endif

                                                            @if($activeSubscription->ends_at)
                                                                <p><strong>Expires:</strong> {{ $activeSubscription->ends_at->format('d M Y') }}</p>
                                                            @else
                                                                <p><strong>Expires:</strong> Never (Ongoing)</p>
                                                            @endif

                                                            <p><strong>Price:</strong> RM {{ number_format($activeSubscription->subscriptionPlan->price, 2) }} / {{ $activeSubscription->subscriptionPlan->billing_frequency }}</p>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="d-flex justify-content-between mt-4">
                                                    <a href="{{ route('user.subscriptions.show', $activeSubscription->id) }}" class="btn btn-primary">
                                                        <i class="bi bi-info-circle"></i> View Details
                                                    </a>
                                                    <div>
                                                        @if($activeSubscription->canceled_at)
                                                            <a href="{{ route('user.subscriptions.resume', $activeSubscription->id) }}" class="btn btn-success">
                                                                <i class="bi bi-arrow-repeat"></i> Resume Subscription
                                                            </a>
                                                        @else
                                                            <a href="{{ route('user.subscriptions.cancel') }}" class="btn btn-danger">
                                                                <i class="bi bi-x-circle"></i> Cancel Subscription
                                                            </a>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @else
                                <div class="alert alert-info">
                                    <h5><i class="bi bi-info-circle"></i> No Active Subscription</h5>
                                    <p>You don't have an active subscription. Choose a plan to get started.</p>
                                </div>
                            @endif
                        </div>


                        <!-- Available Plans Tab -->
                        <div class="tab-pane fade" id="plans" role="tabpanel" aria-labelledby="plans-tab">
                             <div class="d-flex justify-content-center mb-4">
                                <div class="billing-toggle-container">
                                    <div class="btn-group" role="group" aria-label="Billing Frequency Toggle">
                                        <button type="button" class="btn btn-outline-primary billing-toggle" data-frequency="monthly">Monthly</button>
                                        <button type="button" class="btn btn-primary billing-toggle active" data-frequency="yearly">Yearly</button>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-4">
                                @forelse($plans as $plan)
                                    <div class="col-md-4 mb-4 plan-card" data-frequency="{{ $plan->billing_frequency }}">
                                        <div class="card h-100 shadow {{ $activeSubscription && $activeSubscription->subscriptionPlan && $activeSubscription->subscriptionPlan->id === $plan->id ? 'border border-secondary' : '' }} position-relative">
                                            {{-- @if($plan->billing_frequency == 'yearly')
                                            <div class="badge bg-success save-badge">Save 10%</div>
                                            @endif --}}
                                            <div class="badge bg-{{ $activeSubscription && $activeSubscription->subscriptionPlan && $activeSubscription->subscriptionPlan->id === $plan->id ? 'success' : 'secondary' }}">
                                                <h5 class="mb-0 text-white">{{ $plan->name }}</h5>
                                            </div>
                                            <div class="card-body d-flex flex-column">
                                                <div class="text-center mb-3">
                                                    <h3 class="text-primary mb-0">RM{{ number_format($plan->price, 2) }}</h3>
                                                    <small class="text-muted text-capitalize">{{ $plan->billing_frequency }}</small>
                                                </div>

                                                @if(count($allFeatures) > 0)
                                                <ul class="list-group mb-3">
                                                    @foreach($allFeatures as $feature)
                                                        <li class="list-group-item d-flex justify-content-between align-items-center">
                                                            <span>{{ $feature->name }}</span>
                                                            @if($plan->features()->get()->contains('id', $feature->id))
                                                                <i class="bi bi-check-circle-fill text-success"></i>
                                                            @else
                                                                <i class="bi bi-x-circle-fill text-danger"></i>
                                                            @endif
                                                        </li>
                                                    @endforeach
                                                </ul>
                                                @endif

                                                @if($plan->trial_period_days > 0)
                                                    <div class="alert alert-info">
                                                        <i class="bi bi-info-circle me-1"></i>
                                                        Includes {{ $plan->trial_period_days }} days free trial
                                                    </div>
                                                @endif

                                                <div class="mt-auto">
                                                    @if($activeSubscription && $activeSubscription->subscriptionPlan && $activeSubscription->subscriptionPlan->id === $plan->id)
                                                        <button class="btn btn-outline-primary w-100" disabled>Current Plan</button>
                                                    @elseif($activeSubscription && $activeSubscription->subscriptionPlan && $plan->price > $activeSubscription->subscriptionPlan->price)
                                                        <a href="{{ route('user.subscriptions.upgrade.form', $plan->id) }}" class="btn btn-primary w-100">
                                                            Upgrade to This Plan
                                                        </a>
                                                    @elseif($activeSubscription)
                                                        <form action="{{ route('user.subscriptions.subscribe', $plan->id) }}" method="POST">
                                                            @csrf
                                                            <button type="submit" class="btn btn-primary w-100">
                                                                Switch to This Plan
                                                            </button>
                                                        </form>
                                                    @else
                                                        <form action="{{ route('user.subscriptions.subscribe', $plan->id) }}" method="POST">
                                                            @csrf
                                                            <button type="submit" class="btn btn-primary w-100">
                                                                Subscribe Now
                                                            </button>
                                                        </form>
                                                    @endif

                                                    <a href="{{ route('user.subscriptions.plan.show', $plan->id) }}" class="btn btn-link w-100 mt-2">View Details</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @empty
                                    <div class="col-12">
                                        <div class="alert alert-info">
                                            <h4 class="alert-heading"><i class="bi bi-info-circle"></i> No Plans Available</h4>
                                            <p>There are no subscription plans available at the moment.</p>
                                        </div>
                                    </div>
                                @endforelse

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize Bootstrap tabs
        var triggerTabList = [].slice.call(document.querySelectorAll('#subscriptionTabs button'))
        triggerTabList.forEach(function (triggerEl) {
            var tabTrigger = new bootstrap.Tab(triggerEl)
            triggerEl.addEventListener('click', function (event) {
                event.preventDefault()
                tabTrigger.show()
            })
        })

          // Initialize billing frequency toggle
        const billingToggles = document.querySelectorAll('.billing-toggle');
        const planCards = document.querySelectorAll('.plan-card');

        // Function to filter plans by frequency
        function filterPlansByFrequency(frequency) {
            planCards.forEach(card => {
                if (card.dataset.frequency === frequency) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        // Add click event to toggle buttons
        billingToggles.forEach(toggle => {
            toggle.addEventListener('click', function() {
                // Update active state on buttons
                billingToggles.forEach(btn => {
                    btn.classList.remove('btn-primary');
                    btn.classList.add('btn-outline-primary');
                    btn.classList.remove('active');
                });

                this.classList.remove('btn-outline-primary');
                this.classList.add('btn-primary');
                this.classList.add('active');

                // Filter plans
                filterPlansByFrequency(this.dataset.frequency);
            });
        });

        // Initialize with yearly plans (default)
        filterPlansByFrequency('yearly');

    });
</script>
@endpush
